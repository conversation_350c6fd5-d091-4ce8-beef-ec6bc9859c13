import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MnmFormState } from '@injaz/shared/components';
import { NotificationService } from 'mnm-webapp';
import { FormBuilder } from '@angular/forms';
import { fields } from './fields';
import { finalize, takeUntil } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { appFunctions } from '@injaz/common/misc/appFunctions';
import { AppSettingFetcherService } from '@injaz/core/services';
import { Subject } from 'rxjs';
import { NewUserRequestService } from '@injaz/pages/new-user-request/new-user-request.service';
import { Router } from '@angular/router';
import {
    DepartmentSearchDto,
    DepartmentTreeDto,
} from '@injaz/features/departments-tree/department-selector.component';

@Component({ templateUrl: './new-user-request.component.html' })
export class NewUserRequestComponent implements OnInit, On<PERSON><PERSON>roy {
    public isSubmitting = false;
    public formState: MnmFormState;
    public currentLanguage: string = 'en';
    public organizationTypeId?: string;
    public selectedDepartment?: DepartmentSearchDto | DepartmentTreeDto;

    private readonly unsubscribeAll = new Subject();

    public constructor(
        private readonly notificationService: NotificationService,
        private readonly newUserRequestService: NewUserRequestService,
        private readonly translateService: TranslateService,
        private readonly appSettingFetcherService: AppSettingFetcherService,
        private readonly router: Router,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        this.formState.get('gender').items = appFunctions.translateList(
            appFunctions.genders,
            translateService
        );

        // Set current language from translate service
        this.currentLanguage = this.translateService.currentLang || 'en';
    }

    public ngOnInit(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(setting => {
                const field = this.formState.get('email');
                field.emailSuffix = setting.appEmailDomain;
            });
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();
    }

    public onDepartmentSelected(
        department: DepartmentSearchDto | DepartmentTreeDto | undefined
    ): void {
        this.selectedDepartment = department;
        // Update the form control with the selected department
        this.formState.group.patchValue({
            department: department,
        });
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        this.newUserRequestService
            .create(this.formState.group.getRawValue())
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(() => {
                this.notificationService.notifySuccess(
                    this.translateService.instant(
                        'translate_new_user_request_submitted_successfully'
                    )
                );

                this.router.navigate(['', 'auth', 'login']).then();
            });
    }
}
