import { MnmFormField } from '@injaz/shared/components';
import { CustomValidators } from 'mnm-webapp';
import { Validators } from '@angular/forms';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'nameAr',
        type: 'text',
        label: 'translate_name_in_arabic',
        validators: [
            Validators.required,
            Validators.maxLength(256),
            Validators.minLength(3),
        ],
    },

    {
        name: 'nameEn',
        type: 'text',
        label: 'translate_name_in_english',
        validators: [
            Validators.required,
            Validators.maxLength(256),
            Validators.minLength(3),
        ],
    },

    {
        name: 'employeeNumber',
        type: 'text',
        label: 'translate_employee_number',
        validators: [Validators.required],
    },

    {
        name: 'department',
        type: 'select',
        label: 'translate_department',
        bindLabel: 'name',
        hide: true,
        validators: [Validators.required],
    },

    {
        name: 'phoneNumber',
        type: 'text',
        label: 'translate_phone_number',
        validators: [Validators.required],
    },

    {
        name: 'rank',
        type: 'text',
        label: 'translate_rank',
        validators: [Validators.required],
    },

    {
        name: 'email',
        type: 'email',
        label: 'translate_email',
        placeholder: '<EMAIL>',
        validators: [Validators.required, Validators.email],
    },

    {
        name: 'gender',
        type: 'select',
        label: 'translate_gender',
        bindLabel: 'name',
        bindValue: 'id',
        compareWith: (a, b) => a.id === b,
        validators: [Validators.required],
    },

    {
        name: 'requestedPermissions',
        type: 'textarea',
        label: 'translate_requested_permissions',
        validators: [Validators.required],
    },
    {
        name: 'password',
        type: 'password',
        label: 'translate_password',
        validators: [Validators.required],
    },
    {
        name: 'passwordConfirmation',
        type: 'password',
        label: 'translate_password_confirmation',
        validators: [CustomValidators.match('password')],
    },
];
