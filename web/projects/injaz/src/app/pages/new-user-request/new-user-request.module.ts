import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '@injaz/shared/shared.module';
import { TranslationModule } from '@ng-omar/translation';
import { EasterModule } from '@injaz/features/easter/easter.module';
import { NewUserRequestRouting } from './new-user-request.routing';
import { NewUserRequestService } from '@injaz/pages/new-user-request/new-user-request.service';
import { NewUserRequestComponent } from '@injaz/pages/new-user-request/new-user-request.component';
import { DepartmentModule } from '@injaz/features/departments-tree/department.module';

@NgModule({
    declarations: [NewUserRequestComponent],
    imports: [
        CommonModule,
        NewUserRequestRouting,
        FormsModule,
        TranslationModule,
        SharedModule,
        EasterModule,
        DepartmentModule,
    ],
    providers: [NewUserRequestService],
    exports: [NewUserRequestComponent],
})
export class NewUserRequestModule {}
