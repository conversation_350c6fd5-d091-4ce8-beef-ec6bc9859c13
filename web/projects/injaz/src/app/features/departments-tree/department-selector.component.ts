import {
    Component,
    Input,
    Output,
    EventE<PERSON>ter,
    OnInit,
    OnDestroy,
    ViewChild,
    ElementRef,
    HostListener,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import {
    debounceTime,
    distinctUntilChanged,
    switchMap,
    takeUntil,
    startWith,
} from 'rxjs/operators';
import { DepartmentService } from './department.service';

export interface DepartmentTreeDto {
    id: string;
    name: string;
    fullPath: string;
    levelNumber: number;
    order: number;
    isMain: boolean;
    isPoliceStation: boolean;
    parentDepartmentId?: string;
    children: DepartmentTreeDto[];
}

export interface DepartmentSearchDto {
    id: string;
    name: string;
    fullPath: string;
    parentNames: string;
    levelNumber: number;
    isMain: boolean;
    isPoliceStation: boolean;
}

@Component({
    selector: 'app-department-selector',
    template: `
        <div
            class="department-selector"
            [attr.dir]="currentLanguage === 'ar' ? 'rtl' : 'ltr'"
        >
            <!-- Search Input with Selected Tags -->
            <div class="search-input-container">
                <div class="form-group">
                    <label class="form-label">
                        {{
                            currentLanguage === 'ar'
                                ? 'البحث عن القسم'
                                : 'Search Department'
                        }}
                    </label>
                    <div
                        class="search-input-wrapper"
                        (click)="focusSearchInput()"
                    >
                        <!-- Selected Department Tags -->
                        <div
                            class="selected-tags"
                            *ngIf="selectedDepartments.length > 0"
                        >
                            <span
                                *ngFor="
                                    let dept of selectedDepartments;
                                    trackBy: trackByDepartmentId
                                "
                                class="selected-tag"
                            >
                                {{ dept.name }}
                                <button
                                    type="button"
                                    class="tag-remove-btn"
                                    (click)="
                                        removeDepartment(dept);
                                        $event.stopPropagation()
                                    "
                                >
                                    <i class="fas fa-times"></i>
                                </button>
                            </span>
                        </div>

                        <!-- Search Input -->
                        <input
                            #searchInput
                            type="text"
                            class="search-input"
                            [formControl]="searchControl"
                            [placeholder]="getSearchPlaceholder()"
                            autocomplete="off"
                            (focus)="onSearchFocus()"
                            (blur)="onSearchBlur()"
                        />

                        <!-- Dropdown Toggle -->
                        <button
                            type="button"
                            class="dropdown-toggle-btn"
                            (click)="toggleDropdown(); $event.stopPropagation()"
                        >
                            <i
                                class="fas"
                                [class.fa-chevron-down]="!isDropdownOpen"
                                [class.fa-chevron-up]="isDropdownOpen"
                            ></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Search Mode -->
            <div *ngIf="selectionMode === 'search'" class="search-mode">
                <div class="form-group">
                    <label class="form-label">
                        {{
                            currentLanguage === 'ar'
                                ? 'البحث عن القسم'
                                : 'Search Department'
                        }}
                    </label>
                    <input
                        type="text"
                        class="form-control"
                        [formControl]="searchControl"
                        [placeholder]="
                            currentLanguage === 'ar'
                                ? 'اكتب لبدء البحث...'
                                : 'Type to search...'
                        "
                        autocomplete="off"
                    />
                </div>

                <!-- Search Results -->
                <div
                    class="search-results mt-2"
                    *ngIf="searchResults.length > 0"
                >
                    <div class="list-group max-height-300 overflow-auto">
                        <button
                            type="button"
                            class="list-group-item list-group-item-action"
                            *ngFor="
                                let dept of searchResults;
                                trackBy: trackByDepartmentId
                            "
                            [class.active]="selectedDepartment?.id === dept.id"
                            (click)="selectDepartment(dept)"
                        >
                            <div
                                class="d-flex justify-content-between align-items-start"
                            >
                                <div class="department-info flex-grow-1">
                                    <div class="department-name fw-bold">
                                        {{ dept.name }}
                                    </div>
                                    <div
                                        class="department-path text-muted small"
                                        *ngIf="dept.parentNames"
                                    >
                                        <i class="fas fa-sitemap me-1"></i>
                                        {{ dept.parentNames }}
                                    </div>
                                    <div
                                        class="department-level text-muted small"
                                    >
                                        {{
                                            currentLanguage === 'ar'
                                                ? 'المستوى'
                                                : 'Level'
                                        }}
                                        {{ dept.levelNumber }}
                                        <span
                                            *ngIf="dept.isMain"
                                            class="badge ms-1 bg-primary"
                                        >
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'رئيسي'
                                                    : 'Main'
                                            }}
                                        </span>
                                        <span
                                            *ngIf="dept.isPoliceStation"
                                            class="bg-success badge ms-1"
                                        >
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'مركز شرطة'
                                                    : 'Police Station'
                                            }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- No Results -->
                <div
                    *ngIf="searchExecuted && searchResults.length === 0"
                    class="alert alert-info"
                >
                    {{
                        currentLanguage === 'ar'
                            ? 'لا توجد نتائج'
                            : 'No results found'
                    }}
                </div>

                <!-- Loading -->
                <div *ngIf="isLoading" class="mt-2 text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <!-- Tree Mode -->
            <!-- Tree View (when not searching) -->
            <div
                *ngIf="!searchControl.value || searchControl.value.length === 0"
                class="tree-view"
            >
                <div *ngIf="isLoading" class="loading-spinner">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">
                        {{
                            currentLanguage === 'ar'
                                ? 'جاري التحميل...'
                                : 'Loading...'
                        }}
                    </span>
                </div>

                <div
                    *ngIf="!isLoading && departmentTree.length === 0"
                    class="no-results"
                >
                    <div class="no-results-message">
                        {{
                            currentLanguage === 'ar'
                                ? 'لا توجد أقسام متاحة'
                                : 'No departments available'
                        }}
                    </div>
                </div>

                <div *ngIf="!isLoading && departmentTree.length > 0">
                    <div
                        *ngFor="
                            let department of departmentTree;
                            trackBy: trackByDepartmentId
                        "
                        class="tree-item"
                    >
                        <div class="tree-node-content">
                            <button
                                *ngIf="
                                    department.children &&
                                    department.children.length > 0
                                "
                                type="button"
                                class="tree-toggle"
                                (click)="toggleTreeNode(department)"
                            >
                                <i
                                    class="fas"
                                    [class.fa-chevron-right]="
                                        !isTreeNodeExpanded(department)
                                    "
                                    [class.fa-chevron-down]="
                                        isTreeNodeExpanded(department)
                                    "
                                >
                                </i>
                            </button>
                            <div class="checkbox-wrapper" style="flex: 1;">
                                <input
                                    type="checkbox"
                                    [id]="'tree-dept-' + department.id"
                                    [checked]="isDepartmentSelected(department)"
                                    (change)="
                                        toggleDepartmentSelection(department)
                                    "
                                    (click)="$event.stopPropagation()"
                                />
                                <label
                                    [for]="'tree-dept-' + department.id"
                                    class="checkbox-label"
                                >
                                    <div class="department-info">
                                        <div class="department-name">
                                            {{ department.name }}
                                        </div>
                                    </div>
                                    <div class="department-badges">
                                        <span
                                            *ngIf="department.isMain"
                                            class="badge badge-primary"
                                        >
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'رئيسي'
                                                    : 'Main'
                                            }}
                                        </span>
                                        <span
                                            *ngIf="department.isPoliceStation"
                                            class="badge badge-info"
                                        >
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'مركز شرطة'
                                                    : 'Police Station'
                                            }}
                                        </span>
                                        <span class="badge-secondary badge">
                                            {{
                                                currentLanguage === 'ar'
                                                    ? 'مستوى'
                                                    : 'Level'
                                            }}
                                            {{ department.levelNumber }}
                                        </span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Department Display -->
            <div
                *ngIf="selectedDepartment"
                class="selected-department bg-light mt-3 rounded border p-3"
            >
                <h6 class="mb-2">
                    {{
                        currentLanguage === 'ar'
                            ? 'القسم المختار'
                            : 'Selected Department'
                    }}
                </h6>
                <div class="department-name fw-bold">
                    {{ selectedDepartment.name }}
                </div>
                <div
                    class="department-path text-muted small"
                    *ngIf="selectedDepartment.fullPath"
                >
                    <i class="fas fa-sitemap me-1"></i>
                    {{ selectedDepartment.fullPath }}
                </div>
                <button
                    type="button"
                    class="btn btn-sm btn-outline-danger mt-2"
                    (click)="clearSelection()"
                >
                    {{
                        currentLanguage === 'ar'
                            ? 'إلغاء الاختيار'
                            : 'Clear Selection'
                    }}
                </button>
            </div>
        </div>
    `,
    styles: [
        `
            .department-selector {
                width: 100%;
            }

            .max-height-300 {
                max-height: 300px;
            }

            .max-height-400 {
                max-height: 400px;
            }

            .department-name {
                color: #333;
            }

            .department-path {
                margin-top: 0.25rem;
            }

            .department-level {
                margin-top: 0.25rem;
            }

            .list-group-item {
                border-left: 3px solid transparent;
                transition: all 0.2s;
            }

            .list-group-item:hover {
                border-left-color: #007bff;
            }

            .list-group-item.active {
                border-left-color: #007bff;
                background-color: #f8f9fa;
                color: #333;
            }

            .selection-mode-toggle {
                display: flex;
                gap: 0.5rem;
            }

            .tree-container {
                background-color: #fafafa;
            }

            .selected-department {
                background-color: #e8f4f8 !important;
                border-color: #007bff !important;
            }

            /* New Enhanced Styles */
            .search-input-container {
                position: relative;
            }

            .search-input-wrapper {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                min-height: 38px;
                padding: 4px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: #fff;
                cursor: text;
                gap: 4px;
            }

            .search-input-wrapper:focus-within {
                border-color: #007bff;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }

            .selected-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin-right: 4px;
            }

            .selected-tag {
                display: inline-flex;
                align-items: center;
                padding: 2px 6px;
                background-color: #007bff;
                color: white;
                border-radius: 12px;
                font-size: 12px;
                gap: 4px;
            }

            .tag-remove-btn {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 0;
                width: 14px;
                height: 14px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                font-size: 10px;
            }

            .tag-remove-btn:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .search-input {
                flex: 1;
                border: none;
                outline: none;
                padding: 4px;
                min-width: 120px;
                background: transparent;
            }

            .dropdown-toggle-btn {
                background: none;
                border: none;
                color: #6c757d;
                cursor: pointer;
                padding: 4px;
                margin-left: 4px;
            }

            .dropdown-content {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #ced4da;
                border-top: none;
                border-radius: 0 0 4px 4px;
                max-height: 300px;
                overflow-y: auto;
                z-index: 1000;
                display: none;
            }

            .dropdown-content.show {
                display: block;
            }

            .search-results,
            .tree-view {
                padding: 8px;
            }

            .result-item,
            .tree-item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                cursor: pointer;
            }

            .result-item:hover,
            .tree-item:hover {
                background-color: #f8f9fa;
            }

            .checkbox-wrapper {
                display: flex;
                align-items: flex-start;
                gap: 8px;
            }

            .checkbox-wrapper input[type='checkbox'] {
                margin-top: 2px;
            }

            .checkbox-label {
                flex: 1;
                cursor: pointer;
                margin: 0;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .department-info {
                flex: 1;
            }

            .department-name {
                font-weight: 500;
                color: #333;
                margin-bottom: 2px;
            }

            .department-path {
                font-size: 12px;
                color: #6c757d;
            }

            .department-badges {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;
                margin-left: 8px;
            }

            .badge {
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 10px;
            }

            .badge-primary {
                background-color: #007bff;
                color: white;
            }

            .badge-info {
                background-color: #17a2b8;
                color: white;
            }

            .badge-secondary {
                background-color: #6c757d;
                color: white;
            }

            .no-results {
                padding: 16px;
                text-align: center;
                color: #6c757d;
            }

            .tree-node {
                margin-left: 20px;
            }

            .tree-node-content {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 4px 0;
            }

            .tree-toggle {
                background: none;
                border: none;
                cursor: pointer;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* RTL Support */
            [dir='rtl'] .selected-tags {
                margin-left: 4px;
                margin-right: 0;
            }

            [dir='rtl'] .dropdown-toggle-btn {
                margin-right: 4px;
                margin-left: 0;
            }

            [dir='rtl'] .department-badges {
                margin-right: 8px;
                margin-left: 0;
            }

            [dir='rtl'] .tree-node {
                margin-right: 20px;
                margin-left: 0;
            }

            [dir='rtl'] .list-group-item {
                border-left: none;
                border-right: 3px solid transparent;
            }

            [dir='rtl'] .list-group-item:hover {
                border-right-color: #007bff;
            }

            [dir='rtl'] .list-group-item.active {
                border-right-color: #007bff;
            }
        `,
    ],
})
export class DepartmentSelectorComponent implements OnInit, OnDestroy {
    @Input() public currentLanguage: string = 'en';
    @Input() public organizationTypeId?: string;
    @Input() public selectedDepartment?:
        | DepartmentSearchDto
        | DepartmentTreeDto;
    @Input() public allowMultiple: boolean = false; // New: Allow multiple selection
    @Output() public departmentSelected = new EventEmitter<
        DepartmentSearchDto | DepartmentTreeDto | undefined
    >();
    @Output() public departmentsSelected = new EventEmitter<
        (DepartmentSearchDto | DepartmentTreeDto)[]
    >(); // New: For multiple selection

    @ViewChild('searchInput') public searchInput!: ElementRef<HTMLInputElement>;

    public searchControl = new FormControl('');
    public departmentTree: DepartmentTreeDto[] = [];
    public searchResults: DepartmentSearchDto[] = [];
    public selectedDepartments: (DepartmentSearchDto | DepartmentTreeDto)[] =
        []; // New: Multiple selection
    public isLoading = false;
    public searchExecuted = false;
    public isDropdownOpen = false; // New: Dropdown state
    public expandedNodes = new Set<string>(); // New: Track expanded tree nodes

    private destroy$ = new Subject<void>();

    public constructor(private departmentService: DepartmentService) {}

    public ngOnInit(): void {
        this.setupSearchSubscription();
        this.loadDepartmentTree();

        // Initialize selected departments array if single selection
        if (this.selectedDepartment && !this.allowMultiple) {
            this.selectedDepartments = [this.selectedDepartment];
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    @HostListener('document:click', ['$event'])
    public onDocumentClick(event: Event): void {
        if (!this.isDropdownOpen) return;

        const target = event.target as HTMLElement;
        const component = target.closest('.department-selector');
        if (!component) {
            this.isDropdownOpen = false;
        }
    }

    public focusSearchInput(): void {
        if (this.searchInput) {
            this.searchInput.nativeElement.focus();
        }
        this.isDropdownOpen = true;
    }

    public toggleDropdown(): void {
        this.isDropdownOpen = !this.isDropdownOpen;
        if (this.isDropdownOpen) {
            setTimeout(() => this.focusSearchInput(), 0);
        }
    }

    public onSearchFocus(): void {
        this.isDropdownOpen = true;
    }

    public onSearchBlur(): void {
        // Don't close immediately to allow clicking on dropdown items
        setTimeout(() => {
            // Only close if not clicking within dropdown
        }, 150);
    }

    public getSearchPlaceholder(): string {
        if (this.selectedDepartments.length > 0) {
            return this.currentLanguage === 'ar' ? 'البحث...' : 'Search...';
        }
        return this.currentLanguage === 'ar'
            ? 'اختر قسم أو ابحث...'
            : 'Select department or search...';
    }

    public toggleDepartmentSelection(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): void {
        if (this.allowMultiple) {
            const index = this.selectedDepartments.findIndex(
                d => d.id === department.id
            );
            if (index > -1) {
                this.selectedDepartments.splice(index, 1);
            } else {
                this.selectedDepartments.push(department);
            }
            this.departmentsSelected.emit([...this.selectedDepartments]);
        } else {
            // Single selection
            this.selectedDepartments = [department];
            this.selectedDepartment = department;
            this.departmentSelected.emit(department);
            this.isDropdownOpen = false;
        }
    }

    public isDepartmentSelected(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): boolean {
        return this.selectedDepartments.some(d => d.id === department.id);
    }

    public removeDepartment(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): void {
        const index = this.selectedDepartments.findIndex(
            d => d.id === department.id
        );
        if (index > -1) {
            this.selectedDepartments.splice(index, 1);
            if (this.allowMultiple) {
                this.departmentsSelected.emit([...this.selectedDepartments]);
            } else {
                this.selectedDepartment = undefined;
                this.departmentSelected.emit(undefined);
            }
        }
    }

    public selectDepartment(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): void {
        this.toggleDepartmentSelection(department);
    }

    public clearSelection(): void {
        this.selectedDepartments = [];
        this.selectedDepartment = undefined;
        this.departmentSelected.emit(undefined);
        if (this.allowMultiple) {
            this.departmentsSelected.emit([]);
        }
    }

    public toggleTreeNode(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): void {
        if (this.expandedNodes.has(department.id)) {
            this.expandedNodes.delete(department.id);
        } else {
            this.expandedNodes.add(department.id);
        }
    }

    public isTreeNodeExpanded(
        department: DepartmentSearchDto | DepartmentTreeDto
    ): boolean {
        return this.expandedNodes.has(department.id);
    }

    public trackByDepartmentId(
        _index: number,
        item: DepartmentSearchDto | DepartmentTreeDto
    ): string {
        return item.id;
    }

    private setupSearchSubscription(): void {
        this.searchControl.valueChanges
            .pipe(
                startWith(''),
                debounceTime(300),
                distinctUntilChanged(),
                switchMap((searchTerm: string | null) => {
                    if (!searchTerm || searchTerm.length < 2) {
                        this.searchResults = [];
                        this.searchExecuted = false;
                        return [];
                    }

                    this.isLoading = true;
                    return this.departmentService.searchDepartments({
                        searchTerm,
                        language: this.currentLanguage,
                        organizationTypeId: this.organizationTypeId,
                        maxResults: 50,
                    });
                }),
                takeUntil(this.destroy$)
            )
            .subscribe(results => {
                this.searchResults = results;
                this.isLoading = false;
                this.searchExecuted = true;
            });
    }

    private loadDepartmentTree(): void {
        this.isLoading = true;
        this.departmentService
            .getDepartmentTree(this.currentLanguage, this.organizationTypeId)
            .pipe(takeUntil(this.destroy$))
            .subscribe(tree => {
                this.departmentTree = tree;
                this.isLoading = false;
            });
    }
}
