import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { DepartmentSelectorComponent } from './department-selector.component';
import { DepartmentTreeNodeComponent } from './department-tree-node.component';
import { DepartmentService } from './department.service';

@NgModule({
    declarations: [DepartmentSelectorComponent, DepartmentTreeNodeComponent],
    imports: [CommonModule, ReactiveFormsModule, HttpClientModule],
    providers: [DepartmentService],
    exports: [DepartmentSelectorComponent],
})
export class DepartmentModule {}
