import { Component, Input, Output, EventEmitter } from '@angular/core';
import {
    DepartmentTreeDto,
    DepartmentSearchDto,
} from './department-selector.component';

@Component({
    selector: 'app-department-tree-node',
    template: `
        <div class="tree-node">
            <div
                class="node-content"
                [class.selected]="selectedDepartment?.id === department.id"
                [style.padding-left.px]="department.levelNumber * 20"
                [style.padding-right.px]="
                    currentLanguage === 'ar' ? department.levelNumber * 20 : 0
                "
            >
                <button
                    type="button"
                    class="btn-link collapse-toggle btn btn-sm me-2 p-0"
                    *ngIf="department.children.length > 0"
                    (click)="toggleExpanded()"
                    [attr.aria-expanded]="isExpanded"
                >
                    <i
                        class="fas"
                        [class.fa-chevron-down]="isExpanded"
                        [class.fa-chevron-right]="!isExpanded"
                    ></i>
                </button>

                <div class="node-info flex-grow-1" (click)="selectDepartment()">
                    <div class="department-name">
                        {{ department.name }}
                        <span
                            *ngIf="department.isMain"
                            class="badge ms-1 bg-primary"
                        >
                            {{ currentLanguage === 'ar' ? 'رئيسي' : 'Main' }}
                        </span>
                        <span
                            *ngIf="department.isPoliceStation"
                            class="bg-success badge ms-1"
                        >
                            {{
                                currentLanguage === 'ar'
                                    ? 'مركز شرطة'
                                    : 'Police Station'
                            }}
                        </span>
                    </div>
                </div>
            </div>

            <div
                class="children"
                *ngIf="isExpanded && department.children.length > 0"
            >
                <app-department-tree-node
                    *ngFor="
                        let child of department.children;
                        trackBy: trackByDepartmentId
                    "
                    [department]="child"
                    [selectedDepartment]="selectedDepartment"
                    [currentLanguage]="currentLanguage"
                    (departmentSelected)="onChildSelected($event)"
                >
                </app-department-tree-node>
            </div>
        </div>
    `,
    styles: [
        `
            .tree-node {
                margin-bottom: 0.25rem;
            }

            .node-content {
                display: flex;
                align-items: center;
                padding: 0.5rem;
                border-radius: 0.25rem;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .node-content:hover {
                background-color: #f8f9fa;
            }

            .node-content.selected {
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
            }

            .node-info {
                display: flex;
                align-items: center;
            }

            .department-name {
                font-weight: 500;
            }

            .collapse-toggle {
                border: none !important;
                color: #666;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .collapse-toggle:hover {
                color: #333;
            }

            .children {
                margin-left: 1rem;
                border-left: 1px solid #dee2e6;
            }

            [dir='rtl'] .children {
                margin-left: 0;
                margin-right: 1rem;
                border-left: none;
                border-right: 1px solid #dee2e6;
            }
        `,
    ],
})
export class DepartmentTreeNodeComponent {
    @Input() public department!: DepartmentTreeDto;
    @Input() public selectedDepartment?:
        | DepartmentSearchDto
        | DepartmentTreeDto;
    @Input() public currentLanguage: string = 'en';
    @Output() public departmentSelected = new EventEmitter<DepartmentTreeDto>();

    public isExpanded = false;

    public toggleExpanded(): void {
        this.isExpanded = !this.isExpanded;
    }

    public selectDepartment(): void {
        this.departmentSelected.emit(this.department);
    }

    public onChildSelected(department: DepartmentTreeDto): void {
        this.departmentSelected.emit(department);
    }

    public trackByDepartmentId(
        _index: number,
        item: DepartmentTreeDto
    ): string {
        return item.id;
    }
}
